import { Router as ExpressRouter, Request, Response } from 'express';
import fetch from 'node-fetch';
import { ThreatsCache } from '../services/ThreatsCache';
import { ThreatsService } from '../services/ThreatsService';
import UnifiedWebSocketService from '../services/UnifiedWebSocketService';
import config from '../config';

export default class ThreatsController {
  private router: ExpressRouter;
  private threatsCache: ThreatsCache;
  private threatsService: ThreatsService;
  private unifiedWebSocketService: UnifiedWebSocketService;

  constructor(router: ExpressRouter) {
    this.router = router;

    // Initialize threats cache with Redis configuration
    this.threatsCache = new ThreatsCache();

    // Initialize simplified threats service for WebSocket updates
    this.threatsService = ThreatsService.getInstance({
      threatsCache: this.threatsCache,
      cacheTimeout: 3600, // 1 hour
    });

    // Initialize unified WebSocket service
    this.unifiedWebSocketService = UnifiedWebSocketService.getInstance();
    this.unifiedWebSocketService.setThreatsCache(this.threatsCache);

    this.initializeRoutes();
    this.initializeServices();
  }

  private async initializeServices(): Promise<void> {
    try {
      await this.threatsCache.initialize();
      console.log('ThreatsController: Threats cache initialized successfully');

      await this.threatsService.initialize();
      console.log(
        'ThreatsController: Threats service initialized successfully',
      );
    } catch (error) {
      console.error('ThreatsController: Failed to initialize services:', error);
      throw error;
    }
  }

  initializeRoutes(): void {
    this.router.get(
      '/threats',
      async (req: Request, res: Response): Promise<void> => {
        try {
          // First, check if we have valid (non-expired) cached data
          let hasValidCache = false;
          let cachedData = null;

          try {
            hasValidCache = await this.threatsCache.hasValidThreatsData();
            if (hasValidCache) {
              cachedData = await this.threatsCache.getThreatsData();
            }
          } catch (cacheError) {
            console.warn(
              'ThreatsController: Cache check failed, falling back to HTTP:',
              cacheError,
            );
            hasValidCache = false;
            cachedData = null;
          }

          if (hasValidCache && cachedData) {
            console.log('ThreatsController: Serving threats data from cache');

            // Debug: Log investigation outcomes in cached data
            const outcomes = cachedData.map((threat: any) => threat.investigation_outcome).filter(Boolean);
            console.log('ThreatsController: Investigation outcomes in cache:', [...new Set(outcomes)]);

            res.status(200).json({
              data: cachedData,
              source: 'cache',
              timestamp: new Date().toISOString(),
            });
            return;
          }

          // Fallback to HTTP request if no valid cached data is available
          console.log(
            'ThreatsController: No valid cached data available, fetching from HTTP API',
          );
          const response = await fetch(config.externalApi.threatsApiUrl);

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const threatsData = await response.json();

          // Try to cache the HTTP response for future requests
          let cached = false;
          try {
            cached = await this.threatsCache.storeThreatsData(threatsData, 300); // 5 minutes TTL
            console.log(
              `ThreatsController: HTTP data ${cached ? 'cached successfully' : 'cache storage failed'}`,
            );

            // Only broadcast if caching succeeded to ensure consistency
            if (cached) {
              try {
                this.unifiedWebSocketService.broadcastThreats(
                  threatsData,
                  'api',
                );
              } catch (wsError) {
                console.warn(
                  'ThreatsController: Failed to broadcast threats data:',
                  wsError,
                );
              }
            }
          } catch (cacheError) {
            console.warn(
              'ThreatsController: Failed to cache HTTP response:',
              cacheError,
            );
            cached = false;
          }

          res.status(200).json({
            data: threatsData,
            source: 'api',
            cached: cached,
            timestamp: new Date().toISOString(),
          });
        } catch (error) {
          console.error(
            'ThreatsController: Error fetching threats data:',
            error,
          );

          // Return error response
          res.status(500).json({
            error: 'Failed to fetch threats data',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );

    // Comprehensive health check endpoint (merged from /health, /cache-test, and /stats)
    this.router.get(
      '/threats/health',
      async (req: Request, res: Response): Promise<void> => {
        try {
          // Cache health check and status
          const cacheStatus = this.threatsCache.getConnectionStatus();
          const isCacheHealthy = await this.threatsCache.healthCheck();
          const hasCachedData = await this.threatsCache.hasThreatsData();
          const hasValidCachedData =
            await this.threatsCache.hasValidThreatsData();

          // WebSocket service status and statistics
          const wsStatus = this.threatsService.getConnectionStatus();
          const isWsConnected = this.threatsService.isConnected();
          const threatsStats = this.threatsService.getStats();

          // Cache configuration
          const cacheTimeout = this.threatsService.getCacheTimeout();

          // WebSocket connection count
          const wsConnectionCount =
            this.unifiedWebSocketService.getConnectionCount('threats');

          // Determine overall health status
          const overallStatus =
            cacheStatus === 'connected' && isCacheHealthy && isWsConnected
              ? 'healthy'
              : cacheStatus === 'connected' || isWsConnected
                ? 'degraded'
                : 'unhealthy';

          const httpStatus = overallStatus === 'unhealthy' ? 503 : 200;

          res.status(httpStatus).json({
            status: overallStatus,
            services: {
              cache: {
                status: cacheStatus,
                healthy: isCacheHealthy,
                hasData: hasCachedData,
                hasValidData: hasValidCachedData,
                message: isCacheHealthy
                  ? 'Cache service is healthy'
                  : 'Cache service is unhealthy',
              },
              webSocket: {
                status: wsStatus,
                connected: isWsConnected,
                stats: threatsStats,
              },
              threatsWebSocket: {
                connections: wsConnectionCount,
                status: wsConnectionCount > 0 ? 'active' : 'idle',
              },
            },
            configuration: {
              cacheTimeout: cacheTimeout,
            },
            timestamp: new Date().toISOString(),
          });
        } catch (error) {
          res.status(500).json({
            status: 'unhealthy',
            error: error instanceof Error ? error.message : 'Unknown error',
            message: 'Health check failed',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );
  }

  /**
   * Cleanup method to properly dispose of resources
   */
  public cleanup(): void {
    console.log('ThreatsController: Cleaning up resources');

    try {
      // Cleanup threats service
      this.threatsService.cleanup();

      // Cleanup threats cache
      this.threatsCache.cleanup();

      console.log('ThreatsController: Cleanup completed successfully');
    } catch (error) {
      console.error('ThreatsController: Error during cleanup:', error);
    }
  }
}
