import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface RadarChartDataPoint {
  axis: string;
  value: number;
  maxValue?: number;
}

export interface RadarChartProps {
  data: RadarChartDataPoint[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  levels?: number;
  className?: string;
  color?: string;
  fillOpacity?: number;
  strokeWidth?: number;
  showLabels?: boolean;
  showGrid?: boolean;
  showValues?: boolean;
  dotRadius?: number;
}

export const RadarChart: React.FC<RadarChartProps> = ({
  data,
  width = 400,
  height = 400,
  margin = { top: 50, right: 50, bottom: 50, left: 50 },
  levels = 5,
  className = '',
  color = '#3b82f6',
  fillOpacity = 0.3,
  strokeWidth = 2,
  showLabels = true,
  showGrid = true,
  showValues = false,
  dotRadius = 4,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // Clear previous render

    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;
    const radius = Math.min(innerWidth, innerHeight) / 2;

    // Create main group - center within the available space after margins
    const centerX = margin.left + innerWidth / 2;
    const centerY = margin.top + innerHeight / 2;
    const g = svg
      .append('g')
      .attr('transform', `translate(${centerX}, ${centerY})`);

    // Calculate maximum value for scaling
    const maxValue = d3.max(data, (d) => d.maxValue || d.value) || 1;

    // Create scales - go counter-clockwise to fix axis positioning
    const angleScale = d3
      .scaleLinear()
      .domain([0, data.length])
      .range([0, -2 * Math.PI]);

    const radiusScale = d3
      .scaleLinear()
      .domain([0, maxValue])
      .range([0, radius]); // Draw grid circles if enabled
    if (showGrid) {
      const gridGroup = g.append('g').attr('class', 'radar-grid');

      // Draw concentric circles
      for (let level = 1; level <= levels; level++) {
        const r = (radius * level) / levels;
        gridGroup
          .append('circle')
          .attr('r', r)
          .attr('fill', 'none')
          .attr('stroke', 'currentColor')
          .attr('stroke-width', 1)
          .attr('opacity', 0.2)
          .style('color', 'var(--border-primary)');
      }

      // Draw radial lines
      data.forEach((_, i) => {
        const angle = angleScale(i) - Math.PI / 2; // Start from top
        const x = Math.cos(angle) * radius;
        const y = Math.sin(angle) * radius;

        gridGroup
          .append('line')
          .attr('x1', 0)
          .attr('y1', 0)
          .attr('x2', x)
          .attr('y2', y)
          .attr('stroke', 'currentColor')
          .attr('stroke-width', 1)
          .attr('opacity', 0.2)
          .style('color', 'var(--border-primary)');
      });
    }

    // Create line generator for the radar polygon
    const line = d3
      .lineRadial<RadarChartDataPoint>()
      .angle((_, i) => angleScale(i) - Math.PI / 2) // Start from top
      .radius((d) => radiusScale(d.value))
      .curve(d3.curveLinearClosed);

    // Draw the radar area
    const radarGroup = g.append('g').attr('class', 'radar-area');

    radarGroup
      .append('path')
      .datum(data)
      .attr('d', line)
      .attr('fill', color)
      .attr('fill-opacity', fillOpacity)
      .attr('stroke', color)
      .attr('stroke-width', strokeWidth);

    // Draw labels if enabled
    if (showLabels) {
      const labelsGroup = g.append('g').attr('class', 'radar-labels');

      data.forEach((d, i) => {
        const angle = angleScale(i) - Math.PI / 2; // Start from top
        const labelRadius = radius + 20;
        const x = Math.cos(angle) * labelRadius;
        const y = Math.sin(angle) * labelRadius;

        labelsGroup
          .append('text')
          .attr('x', x)
          .attr('y', y)
          .attr('dy', '0.35em')
          .attr('text-anchor', 'middle')
          .attr('fill', 'currentColor')
          .attr('font-size', '12px')
          .attr('font-weight', '500')
          .style('color', 'var(--text-primary)')
          .text(d.axis);
      });
    }

    // Add scale labels on grid
    if (showGrid && levels > 0) {
      const scaleGroup = g.append('g').attr('class', 'radar-scale');

      for (let level = 1; level <= levels; level++) {
        const value = (maxValue * level) / levels;
        const r = (radius * level) / levels;

        scaleGroup
          .append('text')
          .attr('x', 5)
          .attr('y', -r + 3)
          .attr('fill', 'currentColor')
          .attr('font-size', '10px')
          .style('color', 'var(--text-secondary)')
          .text(Math.round(value * 10) / 10);
      }
    }
  }, [
    data,
    width,
    height,
    margin,
    levels,
    color,
    fillOpacity,
    strokeWidth,
    showLabels,
    showGrid,
    showValues,
    dotRadius,
  ]);

  return (
    <div className={`radar-chart ${className}`}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ overflow: 'visible' }}
      />
    </div>
  );
};
